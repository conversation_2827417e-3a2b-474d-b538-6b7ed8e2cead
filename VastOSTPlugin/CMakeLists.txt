cmake_minimum_required(VERSION 3.10)
project(VastOSTPlugin VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# OST SDK paths
set(OST_SDK_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/../OST-SDK-11.1.1")
set(OST_SDK_INCLUDE_DIR "${OST_SDK_ROOT}/src/include")
set(OST_SDK_LIB_DIR "${OST_SDK_ROOT}/src/lib")

# Check if OST SDK exists
if(NOT EXISTS "${OST_SDK_INCLUDE_DIR}/stspi.h")
    message(FATAL_ERROR "OST SDK not found at ${OST_SDK_ROOT}. Please ensure the SDK is extracted.")
endif()

# Include directories
include_directories(
    ${OST_SDK_INCLUDE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Find required libraries
find_package(PkgConfig REQUIRED)

# Find curl for REST API
find_package(CURL REQUIRED)

# Find JSON library (we'll use nlohmann/json)
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann/json not found, will use header-only version")
    # Add nlohmann/json as header-only
    include(FetchContent)
    FetchContent_Declare(
        nlohmann_json
        GIT_REPOSITORY https://github.com/nlohmann/json.git
        GIT_TAG v3.11.2
    )
    FetchContent_MakeAvailable(nlohmann_json)
endif()

# Find AWS SDK for S3 operations
find_package(AWSSDK QUIET COMPONENTS s3 core)
if(NOT AWSSDK_FOUND)
    message(STATUS "AWS SDK not found, will use minimal S3 implementation")
    # You can add a fallback implementation or require manual installation
endif()

# Source files
set(VAST_PLUGIN_SOURCES
    vastplugin.cpp
    VastStorageServer.cpp
    VastRestClient.cpp
    VastS3Client.cpp
)

# Header files
set(VAST_PLUGIN_HEADERS
    vastplugin.h
    VastStorageServer.h
    VastRestClient.h
    VastS3Client.h
)

# Create shared library (OST plugin)
add_library(vastost SHARED ${VAST_PLUGIN_SOURCES})

# Set library properties
set_target_properties(vastost PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CXX_VISIBILITY_PRESET hidden
    VISIBILITY_INLINES_HIDDEN ON
)

# Link libraries
target_link_libraries(vastost
    CURL::libcurl
)

# Link nlohmann/json
if(nlohmann_json_FOUND)
    target_link_libraries(vastost nlohmann_json::nlohmann_json)
else()
    target_link_libraries(vastost nlohmann_json)
endif()

# Link AWS SDK if available
if(AWSSDK_FOUND)
    target_link_libraries(vastost ${AWSSDK_LINK_LIBRARIES})
    target_compile_definitions(vastost PRIVATE HAVE_AWS_SDK)
endif()

# Platform-specific settings
if(WIN32)
    target_compile_definitions(vastost PRIVATE _WIN32_WINNT=0x0601)
    target_link_libraries(vastost ws2_32 wininet)
elseif(APPLE)
    target_link_libraries(vastost "-framework CoreFoundation" "-framework Security")
elseif(UNIX)
    target_link_libraries(vastost pthread dl)
endif()

# Install targets
install(TARGETS vastost
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${VAST_PLUGIN_HEADERS}
    DESTINATION include/vastost
)

# Install configuration file
install(FILES vast_config.conf
    DESTINATION etc/vastost
)

# Create package config
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/VastOSTPluginConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

# Export targets
export(TARGETS vastost
    FILE "${CMAKE_CURRENT_BINARY_DIR}/VastOSTPluginTargets.cmake"
)

# Testing (optional)
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Documentation (optional)
option(BUILD_DOCS "Build documentation" OFF)
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        configure_file(${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in
                      ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
        add_custom_target(doc
            ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen" VERBATIM
        )
    endif()
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "Vast Data OST Plugin Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OST SDK path: ${OST_SDK_ROOT}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  CURL found: ${CURL_FOUND}")
message(STATUS "  nlohmann/json found: ${nlohmann_json_FOUND}")
message(STATUS "  AWS SDK found: ${AWSSDK_FOUND}")
message(STATUS "")