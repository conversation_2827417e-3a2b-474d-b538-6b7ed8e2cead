/*
 *************************************************************************
 * Vast Data LSU (Logical Storage Unit) Implementation
 * Represents a Vast Data volume/bucket for NetBackup storage
 *************************************************************************
 */

#include "VastLSU.h"
#include "VastStorageServer.h"
#include "VastS3Client.h"
#include "VastImage.h"
#include <iostream>
#include <cstring>

VastLSU::VastLSU(const std::string& name, VastStorageServer* server)
    : m_name(name)
    , m_server(server)
    , m_total_capacity(1024ULL * 1024 * 1024 * 1024)  // 1TB default
    , m_used_capacity(0)
    , m_available_capacity(m_total_capacity)
    , m_image_count(0)
    , m_last_error(STS_SUCCESS)
    , m_max_transfer_size(64 * 1024 * 1024)  // 64MB
    , m_block_size(64 * 1024)  // 64KB
    , m_lsu_flags(0)
    , m_status("online")
{
    clearError();
}

VastLSU::~VastLSU()
{
    // Clean up image cache
    for (auto* image : m_image_cache) {
        delete image;
    }
    m_image_cache.clear();
}

int VastLSU::initialize(const std::string& bucket_name)
{
    std::cout << "VastLSU: Initializing LSU " << m_name << std::endl;
    
    clearError();
    
    if (bucket_name.empty()) {
        m_bucket_name = m_name;  // Use LSU name as bucket name
    } else {
        m_bucket_name = bucket_name;
    }
    
    m_path = "/" + m_bucket_name;
    
    // TODO: Initialize with actual Vast Data volume/bucket
    // For now, just set up basic properties
    
    std::cout << "VastLSU: Successfully initialized LSU " << m_name 
              << " with bucket " << m_bucket_name << std::endl;
    
    return STS_SUCCESS;
}

int VastLSU::updateInfo()
{
    // TODO: Update capacity and usage information from Vast Data
    // For now, just return success
    return STS_SUCCESS;
}

int VastLSU::setLabel(const std::string& label)
{
    m_label = label;
    return STS_SUCCESS;
}

VastImage* VastLSU::createImage(const sts_image_def_v10_t* image_def, int flags)
{
    if (!image_def) {
        setError(STS_ERR_INVALID_PARAMETER, "Invalid image definition");
        return nullptr;
    }
    
    try {
        VastImage* image = new VastImage(this, image_def);
        int result = image->create(flags);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to create image: " + image->getLastErrorMessage());
            delete image;
            return nullptr;
        }
        
        m_image_cache.push_back(image);
        m_image_count++;
        
        return image;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image creation failed: ") + e.what());
        return nullptr;
    }
}

VastImage* VastLSU::openImage(const sts_image_def_v10_t* image_def, int mode)
{
    if (!image_def) {
        setError(STS_ERR_INVALID_PARAMETER, "Invalid image definition");
        return nullptr;
    }
    
    try {
        VastImage* image = new VastImage(this, image_def);
        int result = image->open(mode);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to open image: " + image->getLastErrorMessage());
            delete image;
            return nullptr;
        }
        
        m_image_cache.push_back(image);
        
        return image;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image opening failed: ") + e.what());
        return nullptr;
    }
}

int VastLSU::deleteImage(const sts_image_def_v10_t* image_def, int async_flag)
{
    if (!image_def) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // TODO: Implement image deletion using S3 API
    // For now, just return success
    return STS_SUCCESS;
}

int VastLSU::getImageList(std::vector<VastImage*>& image_list)
{
    image_list = m_image_cache;
    return STS_SUCCESS;
}

int VastLSU::getImageInfo(const sts_image_def_v10_t* image_def, sts_image_info_v10_t* image_info)
{
    if (!image_def || !image_info) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // TODO: Implement image info retrieval
    return STS_ERR_NOT_IMPLEMENTED;
}

int VastLSU::getLSUInfo(sts_lsu_info_v11_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // Fill in LSU info structure
    lsu_info->version = 11;
    strncpy(lsu_info->lsu_server, m_server->getServerName().c_str(), 
            sizeof(lsu_info->lsu_server) - 1);
    lsu_info->lsu_server[sizeof(lsu_info->lsu_server) - 1] = '\0';
    
    // Fill in LSU definition
    strncpy(lsu_info->lsu_def.sld_name, m_name.c_str(), 
            sizeof(lsu_info->lsu_def.sld_name) - 1);
    lsu_info->lsu_def.sld_name[sizeof(lsu_info->lsu_def.sld_name) - 1] = '\0';
    
    lsu_info->lsu_def.sld_max_transfer = m_max_transfer_size;
    lsu_info->lsu_def.sld_block_size = m_block_size;
    lsu_info->lsu_def.sld_flags = m_lsu_flags;
    
    // Capacity information
    lsu_info->lsu_capacity = m_total_capacity;
    lsu_info->lsu_capacity_phys = m_total_capacity;
    lsu_info->lsu_used = m_used_capacity;
    lsu_info->lsu_used_phys = m_used_capacity;
    lsu_info->lsu_images = m_image_count;
    
    return STS_SUCCESS;
}

int VastLSU::getLSUInfo(sts_lsu_info_v9_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // Fill in basic LSU info for v9
    strncpy(lsu_info->lsu_server, m_server->getServerName().c_str(), 
            sizeof(lsu_info->lsu_server) - 1);
    lsu_info->lsu_server[sizeof(lsu_info->lsu_server) - 1] = '\0';
    
    lsu_info->lsu_capacity = m_total_capacity;
    lsu_info->lsu_capacity_phys = m_total_capacity;
    lsu_info->lsu_used = m_used_capacity;
    lsu_info->lsu_used_phys = m_used_capacity;
    lsu_info->lsu_images = m_image_count;
    
    return STS_SUCCESS;
}

int VastLSU::getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen)
{
    if (!buf || !maxlen) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    std::string config = "{\n";
    config += "  \"name\": \"" + m_name + "\",\n";
    config += "  \"bucket\": \"" + m_bucket_name + "\",\n";
    config += "  \"path\": \"" + m_path + "\",\n";
    config += "  \"status\": \"" + m_status + "\"\n";
    config += "}";
    
    *maxlen = static_cast<sts_uint32_t>(config.length());
    
    if (buflen < *maxlen + 1) {
        return STS_ERR_BUFFER_TOO_SMALL;
    }
    
    strncpy(buf, config.c_str(), buflen - 1);
    buf[buflen - 1] = '\0';
    
    return STS_SUCCESS;
}

int VastLSU::setConfig(const char* buf)
{
    if (!buf) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // TODO: Parse configuration and update LSU settings
    return STS_ERR_NOT_IMPLEMENTED;
}

VastS3Client* VastLSU::getS3Client()
{
    if (m_server) {
        return m_server->getS3Client();
    }
    return nullptr;
}

void VastLSU::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastLSU Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastLSU::clearError()
{
    m_last_error = STS_SUCCESS;
    m_last_error_msg.clear();
}

std::string VastLSU::generateImageKey(const sts_image_def_v10_t* image_def)
{
    if (!image_def) {
        return "";
    }
    
    return std::string(image_def->img_basename) + "_" + std::string(image_def->img_date);
}

int VastLSU::refreshCapacityInfo()
{
    // TODO: Get actual capacity information from Vast Data
    return STS_SUCCESS;
}

int VastLSU::validateImageDefinition(const sts_image_def_v10_t* image_def)
{
    if (!image_def) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    if (strlen(image_def->img_basename) == 0) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    return STS_SUCCESS;
}
