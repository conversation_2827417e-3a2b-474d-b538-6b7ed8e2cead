/*
 *************************************************************************
 * Vast Data REST Client Implementation
 * Handles HTTP communication with Vast Data VMS REST API
 *************************************************************************
 */

#include "VastRestClient.h"
#include <curl/curl.h>
#include <json/json.h>
#include <sstream>
#include <iostream>
#include <ctime>
#include <cstring>

// HTTP response structure for libcurl
struct HttpResponse {
    std::string body;
    std::map<std::string, std::string> headers;
    long response_code;
};

// Callback for writing response data
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, HttpResponse* response) {
    size_t total_size = size * nmemb;
    response->body.append((char*)contents, total_size);
    return total_size;
}

// Callback for writing header data
static size_t HeaderCallback(void* contents, size_t size, size_t nmemb, HttpResponse* response) {
    size_t total_size = size * nmemb;
    std::string header((char*)contents, total_size);
    
    size_t colon_pos = header.find(':');
    if (colon_pos != std::string::npos) {
        std::string key = header.substr(0, colon_pos);
        std::string value = header.substr(colon_pos + 1);
        
        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t\r\n"));
        key.erase(key.find_last_not_of(" \t\r\n") + 1);
        value.erase(0, value.find_first_not_of(" \t\r\n"));
        value.erase(value.find_last_not_of(" \t\r\n") + 1);
        
        response->headers[key] = value;
    }
    
    return total_size;
}

VastRestClient::VastRestClient() 
    : m_timeout_seconds(30), m_verify_ssl(true), m_last_error(0), m_http_client(nullptr) {
    // Initialize libcurl
    curl_global_init(CURL_GLOBAL_DEFAULT);
    m_http_client = curl_easy_init();
}

VastRestClient::~VastRestClient() {
    if (m_http_client) {
        curl_easy_cleanup((CURL*)m_http_client);
    }
    curl_global_cleanup();
}

int VastRestClient::initialize(const std::string& vms_endpoint, bool verify_ssl) {
    clearError();
    
    m_vms_endpoint = vms_endpoint;
    m_verify_ssl = verify_ssl;
    
    // Remove trailing slash if present
    if (!m_vms_endpoint.empty() && m_vms_endpoint.back() == '/') {
        m_vms_endpoint.pop_back();
    }
    
    return 0;
}

void VastRestClient::setDefaultHeaders(const std::map<std::string, std::string>& headers) {
    m_default_headers = headers;
}

void VastRestClient::setTimeout(int timeout_seconds) {
    m_timeout_seconds = timeout_seconds;
}

int VastRestClient::authenticate(const std::string& username, const std::string& password, VastAuthTokens& tokens) {
    clearError();
    
    Json::Value request_body;
    request_body["username"] = username;
    request_body["password"] = password;
    
    Json::StreamWriterBuilder builder;
    std::string json_body = Json::writeString(builder, request_body);
    
    VastApiResponse response;
    int result = post("/api/token/", json_body, response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            tokens.access_token = json_response.get("access_token", "").asString();
            tokens.refresh_token = json_response.get("refresh_token", "").asString();
            tokens.user_type = json_response.get("user_type", "").asString();
            tokens.expires_at = time(nullptr) + json_response.get("expires_in", 3600).asInt();
            
            // Store the access token for future requests
            m_auth_token = tokens.access_token;
            m_default_headers["Authorization"] = "Bearer " + m_auth_token;
        } else {
            setError(-1, "Failed to parse authentication response");
            return -1;
        }
    }
    
    return result;
}

int VastRestClient::refreshToken(const std::string& refresh_token, VastAuthTokens& tokens) {
    clearError();
    
    Json::Value request_body;
    request_body["refresh_token"] = refresh_token;
    
    Json::StreamWriterBuilder builder;
    std::string json_body = Json::writeString(builder, request_body);
    
    VastApiResponse response;
    int result = post("/api/token/refresh/", json_body, response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            tokens.access_token = json_response.get("access_token", "").asString();
            tokens.refresh_token = json_response.get("refresh_token", "").asString();
            tokens.user_type = json_response.get("user_type", "").asString();
            tokens.expires_at = time(nullptr) + json_response.get("expires_in", 3600).asInt();
            
            // Update stored token
            m_auth_token = tokens.access_token;
            m_default_headers["Authorization"] = "Bearer " + m_auth_token;
        } else {
            setError(-1, "Failed to parse token refresh response");
            return -1;
        }
    }
    
    return result;
}

int VastRestClient::logout() {
    clearError();
    
    VastApiResponse response;
    int result = post("/api/token/logout/", "", response);
    
    // Clear stored authentication
    m_auth_token.clear();
    m_default_headers.erase("Authorization");
    
    return result;
}

int VastRestClient::getVmsInfo(std::map<std::string, std::string>& vms_info) {
    clearError();
    
    VastApiResponse response;
    int result = get("/api/system/info/", response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            vms_info["version"] = json_response.get("version", "").asString();
            vms_info["build"] = json_response.get("build", "").asString();
            vms_info["cluster_name"] = json_response.get("cluster_name", "").asString();
            vms_info["cluster_id"] = json_response.get("cluster_id", "").asString();
        }
    }
    
    return result;
}

int VastRestClient::getClusterStatus(std::map<std::string, std::string>& cluster_status) {
    clearError();
    
    VastApiResponse response;
    int result = get("/api/cluster/status/", response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            cluster_status["state"] = json_response.get("state", "").asString();
            cluster_status["health"] = json_response.get("health", "").asString();
            cluster_status["capacity"] = std::to_string(json_response.get("total_capacity", 0).asUInt64());
            cluster_status["used"] = std::to_string(json_response.get("used_capacity", 0).asUInt64());
        }
    }
    
    return result;
}

int VastRestClient::listViews(std::vector<VastViewInfo>& views) {
    clearError();
    
    VastApiResponse response;
    int result = get("/api/views/", response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            for (const auto& view_json : json_response) {
                VastViewInfo view;
                view.name = view_json.get("name", "").asString();
                view.path = view_json.get("path", "").asString();
                view.tenant_name = view_json.get("tenant", "").asString();
                view.policy_name = view_json.get("policy", "").asString();
                view.nfs_enabled = view_json.get("nfs_enabled", false).asBool();
                view.smb_enabled = view_json.get("smb_enabled", false).asBool();
                view.s3_enabled = view_json.get("s3_enabled", false).asBool();
                view.capacity_bytes = view_json.get("capacity", 0).asUInt64();
                view.used_bytes = view_json.get("used", 0).asUInt64();
                
                views.push_back(view);
            }
        }
    }
    
    return result;
}

int VastRestClient::getViewInfo(const std::string& view_name, VastViewInfo& view_info) {
    clearError();
    
    std::string endpoint = "/api/views/" + view_name + "/";
    VastApiResponse response;
    int result = get(endpoint, response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            view_info.name = json_response.get("name", "").asString();
            view_info.path = json_response.get("path", "").asString();
            view_info.tenant_name = json_response.get("tenant", "").asString();
            view_info.policy_name = json_response.get("policy", "").asString();
            view_info.nfs_enabled = json_response.get("nfs_enabled", false).asBool();
            view_info.smb_enabled = json_response.get("smb_enabled", false).asBool();
            view_info.s3_enabled = json_response.get("s3_enabled", false).asBool();
            view_info.capacity_bytes = json_response.get("capacity", 0).asUInt64();
            view_info.used_bytes = json_response.get("used", 0).asUInt64();
        }
    }
    
    return result;
}

int VastRestClient::createView(const VastViewInfo& view_info) {
    clearError();
    
    Json::Value request_body;
    request_body["name"] = view_info.name;
    request_body["path"] = view_info.path;
    request_body["tenant"] = view_info.tenant_name;
    request_body["policy"] = view_info.policy_name;
    request_body["nfs_enabled"] = view_info.nfs_enabled;
    request_body["smb_enabled"] = view_info.smb_enabled;
    request_body["s3_enabled"] = view_info.s3_enabled;
    
    Json::StreamWriterBuilder builder;
    std::string json_body = Json::writeString(builder, request_body);
    
    VastApiResponse response;
    return post("/api/views/", json_body, response);
}

int VastRestClient::deleteView(const std::string& view_name) {
    clearError();
    
    std::string endpoint = "/api/views/" + view_name + "/";
    VastApiResponse response;
    return delete_request(endpoint, response);
}

int VastRestClient::listS3Buckets(std::vector<VastS3BucketInfo>& buckets) {
    clearError();

    // Note: Vast Data doesn't have /api/s3buckets/ endpoint
    // S3 buckets are managed via standard AWS S3 API using credentials from /api/s3keys/
    // This function should use AWS S3 SDK to list buckets
    setError(-1, "S3 bucket listing should use AWS S3 SDK with credentials from /api/s3keys/");
    return -1;
}

int VastRestClient::getS3BucketInfo(const std::string& bucket_name, VastS3BucketInfo& bucket_info) {
    clearError();

    // Note: Vast Data doesn't have /api/s3buckets/ endpoint
    // S3 bucket info should be retrieved via standard AWS S3 API using credentials from /api/s3keys/
    setError(-1, "S3 bucket info should use AWS S3 SDK with credentials from /api/s3keys/");
    return -1;
}

int VastRestClient::createS3Bucket(const VastS3BucketInfo& bucket_info) {
    clearError();

    // Note: Vast Data doesn't have /api/s3buckets/ endpoint
    // S3 buckets should be created via standard AWS S3 API using credentials from /api/s3keys/
    setError(-1, "S3 bucket creation should use AWS S3 SDK with credentials from /api/s3keys/");
    return -1;
}

int VastRestClient::deleteS3Bucket(const std::string& bucket_name) {
    clearError();

    // Note: Vast Data doesn't have /api/s3buckets/ endpoint
    // S3 buckets should be deleted via standard AWS S3 API using credentials from /api/s3keys/
    setError(-1, "S3 bucket deletion should use AWS S3 SDK with credentials from /api/s3keys/");
    return -1;
}

int VastRestClient::listS3Keys(std::vector<VastS3Key>& s3_keys) {
    clearError();
    
    VastApiResponse response;
    int result = get("/api/s3keys/", response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            for (const auto& key_json : json_response) {
                VastS3Key s3_key;
                s3_key.name = key_json.get("name", "").asString();
                s3_key.access_key = key_json.get("access_key", "").asString();
                s3_key.secret_key = key_json.get("secret_key", "").asString();
                s3_key.user_name = key_json.get("user", "").asString();
                s3_key.tenant_name = key_json.get("tenant", "").asString();
                s3_key.created_at = key_json.get("created_at", "").asString();
                s3_key.enabled = key_json.get("enabled", true).asBool();
                
                s3_keys.push_back(s3_key);
            }
        }
    }
    
    return result;
}

int VastRestClient::getS3Key(const std::string& key_name, VastS3Key& s3_key) {
    clearError();
    
    std::string endpoint = "/api/s3keys/" + key_name + "/";
    VastApiResponse response;
    int result = get(endpoint, response);
    
    if (result == 0 && response.success) {
        Json::Reader reader;
        Json::Value json_response;
        
        if (reader.parse(response.body, json_response)) {
            s3_key.name = json_response.get("name", "").asString();
            s3_key.access_key = json_response.get("access_key", "").asString();
            s3_key.secret_key = json_response.get("secret_key", "").asString();
            s3_key.user_name = json_response.get("user", "").asString();
            s3_key.tenant_name = json_response.get("tenant", "").asString();
            s3_key.created_at = json_response.get("created_at", "").asString();
            s3_key.enabled = json_response.get("enabled", true).asBool();
        }
    }
    
    return result;
}

int VastRestClient::createS3Key(const std::string& name, const std::string& user_name, VastS3KeyPair& key_pair) {
    if (!m_authenticated) {
        setError(-1, "Not authenticated");
        return -1;
    }

    // Prepare JSON payload
    std::string json_payload = "{";
    json_payload += "\"name\":\"" + name + "\"";
    if (!user_name.empty()) {
        json_payload += ",\"user\":\"" + user_name + "\"";
    }
    json_payload += ",\"enabled\":true";
    json_payload += "}";

    std::map<std::string, std::string> headers;
    headers["Content-Type"] = "application/json";
    headers["Authorization"] = "Bearer " + m_auth_tokens.access_token;

    VastApiResponse response = makeHttpRequest("POST", "/api/s3keys/", json_payload, headers);
    
    if (!response.success || response.status_code != 201) {
        setError(response.status_code, "Failed to create S3 key: " + response.error_message);
        return -1;
    }

    // Parse response to extract access_key and secret_key
    // Simple JSON parsing for the key fields
    size_t access_key_pos = response.body.find("\"access_key\":");
    size_t secret_key_pos = response.body.find("\"secret_key\":");
    
    if (access_key_pos != std::string::npos && secret_key_pos != std::string::npos) {
        // Extract access_key
        access_key_pos = response.body.find("\"", access_key_pos + 13);
        size_t access_key_end = response.body.find("\"", access_key_pos + 1);
        key_pair.access_key = response.body.substr(access_key_pos + 1, access_key_end - access_key_pos - 1);
        
        // Extract secret_key
        secret_key_pos = response.body.find("\"", secret_key_pos + 13);
        size_t secret_key_end = response.body.find("\"", secret_key_pos + 1);
        key_pair.secret_key = response.body.substr(secret_key_pos + 1, secret_key_end - secret_key_pos - 1);
        
        key_pair.enabled = true;
    } else {
        setError(-1, "Failed to parse S3 key response");
        return -1;
    }

    return 0;
}

int VastRestClient::getS3Keys(std::vector<VastS3KeyPair>& keys) {
    if (!m_authenticated) {
        setError(-1, "Not authenticated");
        return -1;
    }

    std::map<std::string, std::string> headers;
    headers["Authorization"] = "Bearer " + m_auth_tokens.access_token;

    VastApiResponse response = makeHttpRequest("GET", "/api/s3keys/", "", headers);
    
    if (!response.success || response.status_code != 200) {
        setError(response.status_code, "Failed to get S3 keys: " + response.error_message);
        return -1;
    }

    // Parse response to extract S3 keys
    // This is a simplified parser - in production you'd want a proper JSON library
    keys.clear();
    
    size_t pos = 0;
    while ((pos = response.body.find("\"access_key\":", pos)) != std::string::npos) {
        VastS3KeyPair key_pair;
        
        // Extract access_key
        size_t access_key_start = response.body.find("\"", pos + 13);
        size_t access_key_end = response.body.find("\"", access_key_start + 1);
        key_pair.access_key = response.body.substr(access_key_start + 1, access_key_end - access_key_start - 1);
        
        // Find corresponding secret_key
        size_t secret_key_pos = response.body.find("\"secret_key\":", access_key_end);
        if (secret_key_pos != std::string::npos) {
            size_t secret_key_start = response.body.find("\"", secret_key_pos + 13);
            size_t secret_key_end = response.body.find("\"", secret_key_start + 1);
            key_pair.secret_key = response.body.substr(secret_key_start + 1, secret_key_end - secret_key_start - 1);
        }
        
        // Check if enabled
        size_t enabled_pos = response.body.find("\"enabled\":", access_key_end);
        key_pair.enabled = true; // Default to enabled
        if (enabled_pos != std::string::npos) {
            size_t enabled_value_pos = response.body.find("true", enabled_pos);
            key_pair.enabled = (enabled_value_pos != std::string::npos && 
                               enabled_value_pos < response.body.find("false", enabled_pos));
        }
        
        keys.push_back(key_pair);
        pos = access_key_end;
    }

    return 0;
}

int VastRestClient::deleteS3Key(const std::string& access_key) {
    if (!m_authenticated) {
        setError(-1, "Not authenticated");
        return -1;
    }

    std::map<std::string, std::string> headers;
    headers["Authorization"] = "Bearer " + m_auth_tokens.access_token;

    std::string endpoint = "/api/s3keys/" + access_key + "/";
    VastApiResponse response = makeHttpRequest("DELETE", endpoint, "", headers);
    
    if (!response.success || (response.status_code != 200 && response.status_code != 204)) {
        setError(response.status_code, "Failed to delete S3 key: " + response.error_message);
        return -1;
    }

    return 0;
}

// Generic HTTP methods implementation
int VastRestClient::get(const std::string& endpoint, VastApiResponse& response) {
    return makeHttpRequest("GET", buildUrl(endpoint), "", response);
}

int VastRestClient::post(const std::string& endpoint, const std::string& json_body, VastApiResponse& response) {
    return makeHttpRequest("POST", buildUrl(endpoint), json_body, response);
}

int VastRestClient::put(const std::string& endpoint, const std::string& json_body, VastApiResponse& response) {
    return makeHttpRequest("PUT", buildUrl(endpoint), json_body, response);
}

int VastRestClient::patch(const std::string& endpoint, const std::string& json_body, VastApiResponse& response) {
    return makeHttpRequest("PATCH", buildUrl(endpoint), json_body, response);
}

int VastRestClient::delete_request(const std::string& endpoint, VastApiResponse& response) {
    return makeHttpRequest("DELETE", buildUrl(endpoint), "", response);
}

// Private helper methods
std::string VastRestClient::buildUrl(const std::string& endpoint) const {
    std::string url = m_vms_endpoint;
    if (!endpoint.empty() && endpoint[0] != '/') {
        url += "/";
    }
    url += endpoint;
    return url;
}

int VastRestClient::makeHttpRequest(const std::string& method, const std::string& url, 
                                   const std::string& body, VastApiResponse& response) {
    if (!m_http_client) {
        setError(-1, "HTTP client not initialized");
        return -1;
    }
    
    CURL* curl = (CURL*)m_http_client;
    HttpResponse http_response;
    struct curl_slist* headers = nullptr;
    
    // Reset curl handle
    curl_easy_reset(curl);
    
    // Set basic options
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &http_response);
    curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, HeaderCallback);
    curl_easy_setopt(curl, CURLOPT_HEADERDATA, &http_response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, m_timeout_seconds);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    
    // SSL verification
    if (!m_verify_ssl) {
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    }
    
    // Set headers
    headers = curl_slist_append(headers, "Content-Type: application/json");
    headers = curl_slist_append(headers, "Accept: application/json");
    
    for (const auto& header : m_default_headers) {
        std::string header_line = header.first + ": " + header.second;
        headers = curl_slist_append(headers, header_line.c_str());
    }
    
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    
    // Set method and body
    if (method == "GET") {
        curl_easy_setopt(curl, CURLOPT_HTTPGET, 1L);
    } else if (method == "POST") {
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        if (!body.empty()) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, body.length());
        }
    } else if (method == "PUT") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
        if (!body.empty()) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, body.length());
        }
    } else if (method == "PATCH") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PATCH");
        if (!body.empty()) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, body.length());
        }
    } else if (method == "DELETE") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
    
    // Perform the request
    CURLcode res = curl_easy_perform(curl);
    
    // Cleanup headers
    curl_slist_free_all(headers);
    
    if (res != CURLE_OK) {
        setError(res, curl_easy_strerror(res));
        return -1;
    }
    
    // Get response code
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_response.response_code);
    
    // Fill response structure
    response.status_code = http_response.response_code;
    response.body = http_response.body;
    response.headers = http_response.headers;
    response.success = (http_response.response_code >= 200 && http_response.response_code < 300);
    
    if (!response.success) {
        response.error_message = "HTTP " + std::to_string(http_response.response_code);
        setError(http_response.response_code, response.error_message);
    }
    
    return response.success ? 0 : -1;
}

void VastRestClient::setError(int error_code, const std::string& error_msg) {
    m_last_error = error_code;
    m_last_error_msg = error_msg;
}

void VastRestClient::clearError() {
    m_last_error = 0;
    m_last_error_msg.clear();
}

std::string VastRestClient::escapeJsonString(const std::string& input) {
    std::ostringstream escaped;
    for (char c : input) {
        switch (c) {
            case '"': escaped << "\\\""; break;
            case '\\': escaped << "\\\\"; break;
            case '\b': escaped << "\\b"; break;
            case '\f': escaped << "\\f"; break;
            case '\n': escaped << "\\n"; break;
            case '\r': escaped << "\\r"; break;
            case '\t': escaped << "\\t"; break;
            default: escaped << c; break;
        }
    }
    return escaped.str();
}

std::vector<VastS3Key> VastRestClient::getS3Keys() {
    std::string url = baseUrl + "/api/s3keys/";
    std::string response = performRequest("GET", url, "");
    
    std::vector<VastS3Key> keys;
    
    try {
        nlohmann::json jsonResponse = nlohmann::json::parse(response);
        
        if (jsonResponse.contains("data") && jsonResponse["data"].is_array()) {
            for (const auto& keyData : jsonResponse["data"]) {
                VastS3Key key;
                if (keyData.contains("access_key")) {
                    key.accessKey = keyData["access_key"];
                }
                if (keyData.contains("secret_key")) {
                    key.secretKey = keyData["secret_key"];
                }
                if (keyData.contains("id")) {
                    key.keyId = keyData["id"];
                }
                if (keyData.contains("user_id")) {
                    key.userId = keyData["user_id"];
                }
                if (keyData.contains("enabled")) {
                    key.enabled = keyData["enabled"];
                }
                keys.push_back(key);
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error parsing S3 keys response: " << e.what() << std::endl;
    }
    
    return keys;
}

VastS3Key VastRestClient::createS3Key(int userId) {
    std::string url = baseUrl + "/api/s3keys/";
    nlohmann::json requestData;
    requestData["user_id"] = userId;
    requestData["enabled"] = true;
    
    std::string response = performRequest("POST", url, requestData.dump());
    
    VastS3Key key;
    try {
        nlohmann::json jsonResponse = nlohmann::json::parse(response);
        
        if (jsonResponse.contains("access_key")) {
            key.accessKey = jsonResponse["access_key"];
        }
        if (jsonResponse.contains("secret_key")) {
            key.secretKey = jsonResponse["secret_key"];
        }
        if (jsonResponse.contains("id")) {
            key.keyId = jsonResponse["id"];
        }
        if (jsonResponse.contains("user_id")) {
            key.userId = jsonResponse["user_id"];
        }
        if (jsonResponse.contains("enabled")) {
            key.enabled = jsonResponse["enabled"];
        }
    } catch (const std::exception& e) {
        std::cerr << "Error parsing create S3 key response: " << e.what() << std::endl;
    }
    
    return key;
}

bool VastRestClient::deleteS3Key(int keyId) {
    std::string url = baseUrl + "/api/s3keys/" + std::to_string(keyId) + "/";
    std::string response = performRequest("DELETE", url, "");

    // Check if the response indicates success (typically 204 No Content for DELETE)
    // You might need to adjust this based on the actual API response
    return !response.empty() || true; // Assume success if no exception was thrown
}

void VastRestClient::cleanup() {
    // Clear authentication tokens
    m_auth_token.clear();
    m_default_headers.clear();

    // Cleanup HTTP client resources
    if (m_http_client) {
        curl_easy_cleanup((CURL*)m_http_client);
        m_http_client = nullptr;
    }
}

void VastRestClient::setAuthToken(const std::string& token) {
    m_auth_token = token;
    m_default_headers["Authorization"] = "Bearer " + token;
}