#ifndef _STSPLAT_H_
#define _STSPLAT_H_

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <sys/types.h>
#include <pthread.h>
#include <dirent.h>
#include <dlfcn.h>
#include <stdarg.h>
#include <errno.h>
#include <netdb.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>

#include "ststypes.h"

#define stsp_plat_read(sock, buf, len) read(sock, buf, len)
#define stsp_plat_write(sock, buf, cnt) write(sock, buf, cnt)

#define STS_SO_SFX   "dylib"

#ifdef __cplusplus
#define STS_EXTERN extern "C"
#else
#define STS_EXTERN extern
#endif

#ifdef __cplusplus
#define STS_EXPORT extern "C"
#else
#define STS_EXPORT
#endif

#ifdef __cplusplus
#define STS_IMPORT extern "C"
#else
#define STS_IMPORT extern
#endif

#endif /* !_STSPLAT_H_ */