/*
 *************************************************************************
 * Vast Data Storage Server Implementation
 * Handles communication with Vast Data VMS REST API and S3 operations
 *************************************************************************
 */

#include "VastStorageServer.h"
#include "VastRestClient.h"
#include "VastS3Client.h"
#include "VastLSU.h"
#include <iostream>
#include <sstream>
#include <cstring>

// Error code definitions if not available from SDK
#ifndef STS_ERR_NOT_CONNECTED
#define STS_ERR_NOT_CONNECTED -100
#endif

#ifndef STS_ERR_AUTH_FAILED
#define STS_ERR_AUTH_FAILED -101
#endif

#ifndef STS_ERR_BUFFER_TOO_SMALL
#define STS_ERR_BUFFER_TOO_SMALL -102
#endif

VastStorageServer::VastStorageServer()
    : m_connected(false)
    , m_last_error(STS_SUCCESS)
{
    clearError();
}

VastStorageServer::~VastStorageServer()
{
    disconnect();
    
    // Clean up LSU cache
    for (auto& pair : m_lsu_cache) {
        delete pair.second;
    }
    m_lsu_cache.clear();
}

int VastStorageServer::connect(const std::string& server_name,
                              const std::string& username,
                              const std::string& password,
                              const std::string& vms_endpoint,
                              const std::string& s3_endpoint)
{
    std::cout << "VastStorageServer: Connecting to " << server_name << std::endl;
    
    clearError();
    
    if (m_connected) {
        std::cout << "VastStorageServer: Already connected" << std::endl;
        return STS_SUCCESS;
    }

    try {
        // Store connection parameters
        m_server_name = server_name;
        m_username = username;
        m_password = password;
        m_vms_endpoint = vms_endpoint;
        m_s3_endpoint = s3_endpoint;

        // Initialize REST client for VMS API
        m_rest_client = std::make_unique<VastRestClient>();
        int result = m_rest_client->initialize(m_vms_endpoint);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to initialize VMS REST client");
            return result;
        }

        // Authenticate to VMS
        result = authenticateToVms();
        if (result != STS_SUCCESS) {
            setError(result, "VMS authentication failed");
            return result;
        }

        // Initialize S3 client
        result = initializeS3Client();
        if (result != STS_SUCCESS) {
            setError(result, "Failed to initialize S3 client");
            return result;
        }

        m_connected = true;
        std::cout << "VastStorageServer: Successfully connected to Vast Data cluster" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Connection failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::disconnect()
{
    std::cout << "VastStorageServer: Disconnecting from server" << std::endl;
    
    if (!m_connected) {
        return STS_SUCCESS;
    }

    try {
        // Clean up clients
        if (m_s3_client) {
            m_s3_client->cleanup();
            m_s3_client.reset();
        }
        
        if (m_rest_client) {
            m_rest_client->cleanup();
            m_rest_client.reset();
        }

        // Clear authentication tokens
        m_auth_token.clear();
        m_refresh_token.clear();
        
        m_connected = false;
        clearError();
        
        std::cout << "VastStorageServer: Successfully disconnected" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Disconnect failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getServerInfo(sts_server_info_v8_t* server_info)
{
    if (!server_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (!m_connected) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Fill in server information using v8 structure
        strncpy(server_info->srv_server, m_server_name.c_str(), sizeof(server_info->srv_server) - 1);
        server_info->srv_server[sizeof(server_info->srv_server) - 1] = '\0';

        // Set server flags and capabilities
        server_info->srv_flags = STS_SRV_IMAGELIST | STS_SRV_CRED | STS_SRV_CONRW;
        server_info->srv_maxconnect = 10;
        server_info->srv_nconnect = 0;

        // Initialize stream format handlers (placeholder)
        for (int i = 0; i < STS_MAX_STH; i++) {
            server_info->srv_sth[i] = 0;
        }

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get server info: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getLSUList(std::vector<VastLSU*>& lsu_list)
{
    std::cout << "VastStorageServer: Getting LSU list" << std::endl;
    
    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Clear existing list
        lsu_list.clear();

        // For now, create some sample LSUs until REST API is fully implemented
        // TODO: Replace with actual VMS API calls
        for (int i = 0; i < 3; i++) {
            std::string lsu_name = "vast_lsu_" + std::to_string(i);
            std::string bucket_name = "bucket_" + std::to_string(i);

            VastLSU* lsu = new VastLSU(lsu_name, this);
            if (lsu->initialize(bucket_name) == STS_SUCCESS) {
                lsu_list.push_back(lsu);
                m_lsu_cache[lsu_name] = lsu;
            } else {
                delete lsu;
            }
        }

        std::cout << "VastStorageServer: Found " << lsu_list.size() << " LSUs" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get LSU list: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getLSUInfo(const std::string& lsu_name, sts_lsu_info_v11_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Check cache first
        auto it = m_lsu_cache.find(lsu_name);
        VastLSU* lsu = nullptr;
        
        if (it != m_lsu_cache.end()) {
            lsu = it->second;
        } else {
            // Create LSU if not found in cache
            // TODO: Get actual LSU info from VMS API
            lsu = new VastLSU(lsu_name, this);
            if (lsu->initialize() != STS_SUCCESS) {
                delete lsu;
                setError(STS_ERR_INTERNAL, "Failed to initialize LSU");
                return STS_ERR_INTERNAL;
            }
            m_lsu_cache[lsu_name] = lsu;
        }

        // Fill in LSU info structure using getLSUInfo method
        return lsu->getLSUInfo(lsu_info);
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get LSU info: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::createLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Creating LSU: " << lsu_name << std::endl;
    
    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // TODO: Implement actual volume creation using VMS REST API
        // For now, just create a placeholder LSU
        VastLSU* lsu = new VastLSU(lsu_name, this);
        if (lsu->initialize(lsu_name) == STS_SUCCESS) {
            m_lsu_cache[lsu_name] = lsu;
            std::cout << "VastStorageServer: Successfully created LSU: " << lsu_name << std::endl;
            return STS_SUCCESS;
        } else {
            delete lsu;
            setError(STS_ERR_INTERNAL, "Failed to initialize new LSU");
            return STS_ERR_INTERNAL;
        }
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to create LSU: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::deleteLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Deleting LSU: " << lsu_name << std::endl;
    
    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // TODO: Implement actual volume deletion using VMS REST API

        // Remove from cache
        auto it = m_lsu_cache.find(lsu_name);
        if (it != m_lsu_cache.end()) {
            delete it->second;
            m_lsu_cache.erase(it);
            std::cout << "VastStorageServer: Successfully deleted LSU: " << lsu_name << std::endl;
            return STS_SUCCESS;
        } else {
            setError(STS_ERR_INVALID_PARAMETER, "LSU not found");
            return STS_ERR_INVALID_PARAMETER;
        }
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to delete LSU: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::refreshAuthToken()
{
    std::cout << "VastStorageServer: Refreshing authentication token" << std::endl;
    
    if (!m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "REST client not initialized");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // TODO: Implement token refresh using VMS API
        // For now, just re-authenticate
        return authenticateToVms();
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Token refresh failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getServerConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen)
{
    if (!buf || !maxlen) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Return basic server configuration as JSON
    std::ostringstream config;
    config << "{\n";
    config << "  \"server_name\": \"" << m_server_name << "\",\n";
    config << "  \"vms_endpoint\": \"" << m_vms_endpoint << "\",\n";
    config << "  \"s3_endpoint\": \"" << m_s3_endpoint << "\",\n";
    config << "  \"connected\": " << (m_connected ? "true" : "false") << "\n";
    config << "}";

    std::string config_str = config.str();
    *maxlen = static_cast<sts_uint32_t>(config_str.length());

    if (buflen < *maxlen + 1) {
        return STS_ERR_BUFFER_TOO_SMALL;
    }

    strncpy(buf, config_str.c_str(), buflen - 1);
    buf[buflen - 1] = '\0';

    return STS_SUCCESS;
}

int VastStorageServer::setServerConfig(const char* buf, char* msgbuf, sts_uint32_t msgbuflen)
{
    if (!buf) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // For now, return not supported - configuration should be done through VMS
    if (msgbuf && msgbuflen > 0) {
        const char* msg = "Server configuration changes not supported through this interface";
        strncpy(msgbuf, msg, msgbuflen - 1);
        msgbuf[msgbuflen - 1] = '\0';
    }

    return STS_ERR_NOT_SUPPORTED;
}

// Private methods implementation

int VastStorageServer::authenticateToVms()
{
    std::cout << "VastStorageServer: Authenticating to VMS" << std::endl;
    
    if (!m_rest_client) {
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // TODO: Implement actual VMS authentication
        // For now, create a dummy token
        m_auth_token = "dummy_token_" + m_username;
        m_refresh_token = "dummy_refresh_token";

        if (m_rest_client) {
            m_rest_client->setAuthToken(m_auth_token);
        }

        std::cout << "VastStorageServer: Successfully authenticated to VMS" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_AUTH_FAILED, std::string("Authentication failed: ") + e.what());
        return STS_ERR_AUTH_FAILED;
    }
}

int VastStorageServer::initializeS3Client()
{
    std::cout << "VastStorageServer: Initializing S3 client" << std::endl;
    
    try {
        m_s3_client = std::make_unique<VastS3Client>();
        
        // Initialize S3 client with endpoint and credentials
        int result = m_s3_client->initialize(m_s3_endpoint, m_username, m_password);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to initialize S3 client");
            return result;
        }

        std::cout << "VastStorageServer: Successfully initialized S3 client" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("S3 client initialization failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

void VastStorageServer::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastStorageServer Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastStorageServer::clearError()
{
    m_last_error = STS_SUCCESS;
    m_last_error_msg.clear();
}