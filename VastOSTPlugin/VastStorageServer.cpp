/*
 *************************************************************************
 * Vast Data Storage Server Implementation
 * Handles communication with Vast Data VMS REST API and S3 operations
 *************************************************************************
 */

#include "VastStorageServer.h"
#include "VastRestClient.h"
#include "VastS3Client.h"
#include "ststypes.h"
#include "stsplat.h"
#include <iostream>
#include <sstream>
#include <cstring>
#include <curl/curl.h>
#include <json/json.h>

// Forward declaration for VastLSU class
class VastLSU {
public:
    std::string name;
    std::string bucket_name;
    std::string path;
    sts_uint64_t total_capacity;
    sts_uint64_t used_capacity;
    sts_uint64_t available_capacity;
    std::string status;
    
    VastLSU(const std::string& lsu_name, const std::string& bucket) 
        : name(lsu_name), bucket_name(bucket), total_capacity(0), used_capacity(0), available_capacity(0), status("online") {}
};

VastStorageServer::VastStorageServer()
    : m_connected(false)
    , m_last_error(STS_SUCCESS)
{
    clearError();
}

VastStorageServer::~VastStorageServer()
{
    disconnect();
    
    // Clean up LSU cache
    for (auto& pair : m_lsu_cache) {
        delete pair.second;
    }
    m_lsu_cache.clear();
}

int VastStorageServer::connect(const std::string& server_name,
                              const std::string& username,
                              const std::string& password,
                              const std::string& vms_endpoint,
                              const std::string& s3_endpoint)
{
    std::cout << "VastStorageServer: Connecting to " << server_name << std::endl;
    
    clearError();
    
    if (m_connected) {
        std::cout << "VastStorageServer: Already connected" << std::endl;
        return STS_SUCCESS;
    }

    try {
        // Store connection parameters
        m_server_name = server_name;
        m_username = username;
        m_password = password;
        m_vms_endpoint = vms_endpoint;
        m_s3_endpoint = s3_endpoint;

        // Initialize REST client for VMS API
        m_rest_client = std::make_unique<VastRestClient>();
        int result = m_rest_client->initialize(m_vms_endpoint);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to initialize VMS REST client");
            return result;
        }

        // Authenticate to VMS
        result = authenticateToVms();
        if (result != STS_SUCCESS) {
            setError(result, "VMS authentication failed");
            return result;
        }

        // Initialize S3 client
        result = initializeS3Client();
        if (result != STS_SUCCESS) {
            setError(result, "Failed to initialize S3 client");
            return result;
        }

        m_connected = true;
        std::cout << "VastStorageServer: Successfully connected to Vast Data cluster" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Connection failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::disconnect()
{
    std::cout << "VastStorageServer: Disconnecting from server" << std::endl;
    
    if (!m_connected) {
        return STS_SUCCESS;
    }

    try {
        // Clean up clients
        if (m_s3_client) {
            m_s3_client->cleanup();
            m_s3_client.reset();
        }
        
        if (m_rest_client) {
            m_rest_client->cleanup();
            m_rest_client.reset();
        }

        // Clear authentication tokens
        m_auth_token.clear();
        m_refresh_token.clear();
        
        m_connected = false;
        clearError();
        
        std::cout << "VastStorageServer: Successfully disconnected" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Disconnect failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getServerInfo(sts_server_info_t* server_info)
{
    if (!server_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (!m_connected) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Fill in server information
        strncpy(server_info->serverName, m_server_name.c_str(), sizeof(server_info->serverName) - 1);
        server_info->serverName[sizeof(server_info->serverName) - 1] = '\0';
        
        strncpy(server_info->serverType, "Vast Data", sizeof(server_info->serverType) - 1);
        server_info->serverType[sizeof(server_info->serverType) - 1] = '\0';
        
        strncpy(server_info->serverVersion, "5.0", sizeof(server_info->serverVersion) - 1);
        server_info->serverVersion[sizeof(server_info->serverVersion) - 1] = '\0';
        
        // Set capabilities supported by Vast Data
        server_info->capabilities = STS_CAP_ASYNC_IO | STS_CAP_COPY_IMAGE | STS_CAP_EVENTS | 
                                   STS_CAP_METADATA | STS_CAP_DEDUP | STS_CAP_COMPRESSION;

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get server info: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getLSUList(std::vector<VastLSU*>& lsu_list)
{
    std::cout << "VastStorageServer: Getting LSU list" << std::endl;
    
    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Clear existing list
        lsu_list.clear();

        // Get volumes/buckets from VMS using REST API
        Json::Value volumes_response;
        int result = m_rest_client->getVolumes(volumes_response);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to retrieve volumes from VMS");
            return result;
        }

        // Parse volumes and create LSU objects
        if (volumes_response.isArray()) {
            for (const auto& volume : volumes_response) {
                std::string volume_name = volume.get("name", "").asString();
                std::string bucket_name = volume.get("s3_bucket", volume_name).asString();
                
                if (!volume_name.empty()) {
                    VastLSU* lsu = new VastLSU(volume_name, bucket_name);
                    
                    // Fill in capacity information if available
                    lsu->total_capacity = volume.get("total_capacity", 0).asUInt64();
                    lsu->used_capacity = volume.get("used_capacity", 0).asUInt64();
                    lsu->available_capacity = lsu->total_capacity - lsu->used_capacity;
                    lsu->status = volume.get("status", "online").asString();
                    
                    lsu_list.push_back(lsu);
                    
                    // Cache the LSU
                    m_lsu_cache[volume_name] = lsu;
                }
            }
        }

        std::cout << "VastStorageServer: Found " << lsu_list.size() << " LSUs" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get LSU list: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getLSUInfo(const std::string& lsu_name, sts_lsu_info_v11_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Check cache first
        auto it = m_lsu_cache.find(lsu_name);
        VastLSU* lsu = nullptr;
        
        if (it != m_lsu_cache.end()) {
            lsu = it->second;
        } else {
            // Get LSU info from VMS
            Json::Value volume_info;
            int result = m_rest_client->getVolumeInfo(lsu_name, volume_info);
            if (result != STS_SUCCESS) {
                setError(result, "Failed to get volume info from VMS");
                return result;
            }

            // Create and cache LSU
            std::string bucket_name = volume_info.get("s3_bucket", lsu_name).asString();
            lsu = new VastLSU(lsu_name, bucket_name);
            lsu->total_capacity = volume_info.get("total_capacity", 0).asUInt64();
            lsu->used_capacity = volume_info.get("used_capacity", 0).asUInt64();
            lsu->available_capacity = lsu->total_capacity - lsu->used_capacity;
            lsu->status = volume_info.get("status", "online").asString();
            
            m_lsu_cache[lsu_name] = lsu;
        }

        // Fill in LSU info structure
        strncpy(lsu_info->lsuName, lsu->name.c_str(), sizeof(lsu_info->lsuName) - 1);
        lsu_info->lsuName[sizeof(lsu_info->lsuName) - 1] = '\0';
        
        lsu_info->totalCapacity = lsu->total_capacity;
        lsu_info->usedCapacity = lsu->used_capacity;
        lsu_info->availableCapacity = lsu->available_capacity;
        lsu_info->lsuType = STS_LSU_TYPE_VOLUME;
        lsu_info->lsuStatus = (lsu->status == "online") ? STS_LSU_STATUS_ONLINE : STS_LSU_STATUS_OFFLINE;

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get LSU info: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::createLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Creating LSU: " << lsu_name << std::endl;
    
    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Create volume using VMS REST API
        Json::Value create_request;
        create_request["name"] = lsu_name;
        create_request["s3_bucket"] = lsu_name; // Use same name for S3 bucket
        create_request["protocol"] = "S3";
        
        Json::Value response;
        int result = m_rest_client->createVolume(create_request, response);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to create volume in VMS");
            return result;
        }

        std::cout << "VastStorageServer: Successfully created LSU: " << lsu_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to create LSU: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::deleteLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Deleting LSU: " << lsu_name << std::endl;
    
    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Delete volume using VMS REST API
        int result = m_rest_client->deleteVolume(lsu_name);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to delete volume from VMS");
            return result;
        }

        // Remove from cache
        auto it = m_lsu_cache.find(lsu_name);
        if (it != m_lsu_cache.end()) {
            delete it->second;
            m_lsu_cache.erase(it);
        }

        std::cout << "VastStorageServer: Successfully deleted LSU: " << lsu_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to delete LSU: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::refreshAuthToken()
{
    std::cout << "VastStorageServer: Refreshing authentication token" << std::endl;
    
    if (!m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "REST client not initialized");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        if (!m_refresh_token.empty()) {
            // Use refresh token if available
            Json::Value refresh_request;
            refresh_request["refresh_token"] = m_refresh_token;
            
            Json::Value response;
            int result = m_rest_client->refreshToken(refresh_request, response);
            if (result == STS_SUCCESS) {
                m_auth_token = response.get("access_token", "").asString();
                m_refresh_token = response.get("refresh_token", m_refresh_token).asString();
                return STS_SUCCESS;
            }
        }

        // Fall back to re-authentication
        return authenticateToVms();
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Token refresh failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getServerConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen)
{
    if (!buf || !maxlen) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Return basic server configuration as JSON
    std::ostringstream config;
    config << "{\n";
    config << "  \"server_name\": \"" << m_server_name << "\",\n";
    config << "  \"vms_endpoint\": \"" << m_vms_endpoint << "\",\n";
    config << "  \"s3_endpoint\": \"" << m_s3_endpoint << "\",\n";
    config << "  \"connected\": " << (m_connected ? "true" : "false") << "\n";
    config << "}";

    std::string config_str = config.str();
    *maxlen = static_cast<sts_uint32_t>(config_str.length());

    if (buflen < *maxlen + 1) {
        return STS_ERR_BUFFER_TOO_SMALL;
    }

    strncpy(buf, config_str.c_str(), buflen - 1);
    buf[buflen - 1] = '\0';

    return STS_SUCCESS;
}

int VastStorageServer::setServerConfig(const char* buf, char* msgbuf, sts_uint32_t msgbuflen)
{
    if (!buf) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // For now, return not supported - configuration should be done through VMS
    if (msgbuf && msgbuflen > 0) {
        const char* msg = "Server configuration changes not supported through this interface";
        strncpy(msgbuf, msg, msgbuflen - 1);
        msgbuf[msgbuflen - 1] = '\0';
    }

    return STS_ERR_NOT_SUPPORTED;
}

// Private methods implementation

int VastStorageServer::authenticateToVms()
{
    std::cout << "VastStorageServer: Authenticating to VMS" << std::endl;
    
    if (!m_rest_client) {
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Prepare authentication request
        Json::Value auth_request;
        auth_request["username"] = m_username;
        auth_request["password"] = m_password;

        Json::Value response;
        int result = m_rest_client->authenticate(auth_request, response);
        if (result != STS_SUCCESS) {
            return result;
        }

        // Extract tokens from response
        m_auth_token = response.get("access_token", "").asString();
        m_refresh_token = response.get("refresh_token", "").asString();

        if (m_auth_token.empty()) {
            setError(STS_ERR_AUTH_FAILED, "No access token received from VMS");
            return STS_ERR_AUTH_FAILED;
        }

        // Set authorization header for future requests
        m_rest_client->setAuthToken(m_auth_token);

        std::cout << "VastStorageServer: Successfully authenticated to VMS" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_AUTH_FAILED, std::string("Authentication failed: ") + e.what());
        return STS_ERR_AUTH_FAILED;
    }
}

int VastStorageServer::initializeS3Client()
{
    std::cout << "VastStorageServer: Initializing S3 client" << std::endl;
    
    try {
        m_s3_client = std::make_unique<VastS3Client>();
        
        // Initialize S3 client with endpoint and credentials
        int result = m_s3_client->initialize(m_s3_endpoint, m_username, m_password);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to initialize S3 client");
            return result;
        }

        std::cout << "VastStorageServer: Successfully initialized S3 client" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("S3 client initialization failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

void VastStorageServer::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastStorageServer Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastStorageServer::clearError()
{
    m_last_error = STS_SUCCESS;
    m_last_error_msg.clear();
}