/*
 *************************************************************************
 * Vast Data Storage Server Class
 * Handles communication with Vast Data VMS REST API
 *************************************************************************
 */

#ifndef _VAST_STORAGE_SERVER_H_
#define _VAST_STORAGE_SERVER_H_

#include "stspi.h"
#include <string>
#include <vector>
#include <memory>
#include <map>

// Forward declarations
class VastRestClient;
class VastS3Client;
class VastLSU;

class VastStorageServer {
public:
    VastStorageServer();
    ~VastStorageServer();

    // Server connection and authentication
    int connect(const std::string& server_name, 
                const std::string& username, 
                const std::string& password,
                const std::string& vms_endpoint,
                const std::string& s3_endpoint);
    
    int disconnect();
    bool isConnected() const { return m_connected; }

    // Server properties
    int getServerInfo(sts_server_info_t* server_info);
    std::string getServerName() const { return m_server_name; }
    std::string getVmsEndpoint() const { return m_vms_endpoint; }
    std::string getS3Endpoint() const { return m_s3_endpoint; }

    // LSU (Logical Storage Unit) management - maps to Vast Data volumes/buckets
    int getLSUList(std::vector<VastLSU*>& lsu_list);
    int getLSUInfo(const std::string& lsu_name, sts_lsu_info_v11_t* lsu_info);
    int createLSU(const std::string& lsu_name);
    int deleteLSU(const std::string& lsu_name);

    // Authentication token management
    int refreshAuthToken();
    std::string getAuthToken() const { return m_auth_token; }

    // Configuration management
    int getServerConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen);
    int setServerConfig(const char* buf, char* msgbuf, sts_uint32_t msgbuflen);

    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

    // Client accessors
    VastRestClient* getRestClient() { return m_rest_client.get(); }
    VastS3Client* getS3Client() { return m_s3_client.get(); }

private:
    // Connection state
    bool m_connected;
    std::string m_server_name;
    std::string m_vms_endpoint;
    std::string m_s3_endpoint;
    std::string m_username;
    std::string m_password;
    std::string m_auth_token;
    std::string m_refresh_token;

    // Client objects
    std::unique_ptr<VastRestClient> m_rest_client;
    std::unique_ptr<VastS3Client> m_s3_client;

    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;

    // LSU cache
    std::map<std::string, VastLSU*> m_lsu_cache;

    // Private helper methods
    int authenticateToVms();
    int initializeS3Client();
    void setError(int error_code, const std::string& error_msg);
    void clearError();
};

#endif /* _VAST_STORAGE_SERVER_H_ */