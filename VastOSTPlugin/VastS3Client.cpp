/*
 *************************************************************************
 * Vast Data S3 Client Implementation
 * Handles S3 protocol operations for data storage and retrieval
 *************************************************************************
 */

#include "VastS3Client.h"
#include <curl/curl.h>
#include <openssl/hmac.h>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <ctime>
#include <cstring>

// HTTP response structure for libcurl
struct S3Response {
    std::string body;
    std::map<std::string, std::string> headers;
    long response_code;
    FILE* output_file;
    
    S3Response() : response_code(0), output_file(nullptr) {}
};

// Callback for writing response data
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, S3Response* response) {
    size_t total_size = size * nmemb;
    
    if (response->output_file) {
        return fwrite(contents, size, nmemb, response->output_file);
    } else {
        response->body.append((char*)contents, total_size);
        return total_size;
    }
}

// Callback for reading request data
static size_t ReadCallback(void* ptr, size_t size, size_t nmemb, FILE* stream) {
    return fread(ptr, size, nmemb, stream);
}

// Callback for writing header data
static size_t HeaderCallback(void* contents, size_t size, size_t nmemb, S3Response* response) {
    size_t total_size = size * nmemb;
    std::string header((char*)contents, total_size);
    
    size_t colon_pos = header.find(':');
    if (colon_pos != std::string::npos) {
        std::string key = header.substr(0, colon_pos);
        std::string value = header.substr(colon_pos + 1);
        
        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t\r\n"));
        key.erase(key.find_last_not_of(" \t\r\n") + 1);
        value.erase(0, value.find_first_not_of(" \t\r\n"));
        value.erase(value.find_last_not_of(" \t\r\n") + 1);
        
        response->headers[key] = value;
    }
    
    return total_size;
}

VastS3Client::VastS3Client() 
    : m_timeout_seconds(300), m_verify_ssl(true), m_last_error(0), m_http_client(nullptr) {
    // Initialize libcurl
    curl_global_init(CURL_GLOBAL_DEFAULT);
    m_http_client = curl_easy_init();
}

VastS3Client::~VastS3Client() {
    cleanup();
}

void VastS3Client::cleanup() {
    if (m_http_client) {
        curl_easy_cleanup((CURL*)m_http_client);
        m_http_client = nullptr;
    }
    curl_global_cleanup();
}

int VastS3Client::initialize(const std::string& s3_endpoint, const std::string& access_key, 
                            const std::string& secret_key, const std::string& region, bool verify_ssl) {
    clearError();
    
    m_s3_endpoint = s3_endpoint;
    m_access_key = access_key;
    m_secret_key = secret_key;
    m_region = region.empty() ? "us-east-1" : region;
    m_verify_ssl = verify_ssl;
    
    // Remove trailing slash if present
    if (!m_s3_endpoint.empty() && m_s3_endpoint.back() == '/') {
        m_s3_endpoint.pop_back();
    }
    
    return 0;
}

void VastS3Client::setTimeout(int timeout_seconds) {
    m_timeout_seconds = timeout_seconds;
}

int VastS3Client::listBuckets(std::vector<VastS3BucketInfo>& buckets) {
    clearError();
    
    S3Response response;
    int result = makeS3Request("GET", "", "", "", {}, response);
    
    if (result == 0) {
        // Parse XML response for bucket list
        // This is a simplified parser - in production, use a proper XML library
        size_t pos = 0;
        while ((pos = response.body.find("<Bucket>", pos)) != std::string::npos) {
            size_t end_pos = response.body.find("</Bucket>", pos);
            if (end_pos == std::string::npos) break;
            
            std::string bucket_xml = response.body.substr(pos, end_pos - pos + 9);
            
            VastS3BucketInfo bucket;
            
            // Extract bucket name
            size_t name_start = bucket_xml.find("<Name>") + 6;
            size_t name_end = bucket_xml.find("</Name>");
            if (name_start != std::string::npos && name_end != std::string::npos) {
                bucket.name = bucket_xml.substr(name_start, name_end - name_start);
            }
            
            // Extract creation date
            size_t date_start = bucket_xml.find("<CreationDate>") + 14;
            size_t date_end = bucket_xml.find("</CreationDate>");
            if (date_start != std::string::npos && date_end != std::string::npos) {
                bucket.creation_date = bucket_xml.substr(date_start, date_end - date_start);
            }
            
            buckets.push_back(bucket);
            pos = end_pos + 9;
        }
    }
    
    return result;
}

int VastS3Client::createBucket(const std::string& bucket_name) {
    clearError();
    
    S3Response response;
    return makeS3Request("PUT", bucket_name, "", "", {}, response);
}

int VastS3Client::deleteBucket(const std::string& bucket_name) {
    clearError();
    
    S3Response response;
    return makeS3Request("DELETE", bucket_name, "", "", {}, response);
}

int VastS3Client::headBucket(const std::string& bucket_name) {
    clearError();
    
    S3Response response;
    return makeS3Request("HEAD", bucket_name, "", "", {}, response);
}

int VastS3Client::listObjects(const std::string& bucket_name, const std::string& prefix, 
                             std::vector<VastS3ObjectInfo>& objects, const std::string& continuation_token) {
    clearError();
    
    std::map<std::string, std::string> query_params;
    query_params["list-type"] = "2";
    if (!prefix.empty()) {
        query_params["prefix"] = prefix;
    }
    if (!continuation_token.empty()) {
        query_params["continuation-token"] = continuation_token;
    }
    
    S3Response response;
    int result = makeS3Request("GET", bucket_name, "", "", query_params, response);
    
    if (result == 0) {
        // Parse XML response for object list
        size_t pos = 0;
        while ((pos = response.body.find("<Contents>", pos)) != std::string::npos) {
            size_t end_pos = response.body.find("</Contents>", pos);
            if (end_pos == std::string::npos) break;
            
            std::string object_xml = response.body.substr(pos, end_pos - pos + 11);
            
            VastS3ObjectInfo object;
            
            // Extract key
            size_t key_start = object_xml.find("<Key>") + 5;
            size_t key_end = object_xml.find("</Key>");
            if (key_start != std::string::npos && key_end != std::string::npos) {
                object.key = object_xml.substr(key_start, key_end - key_start);
            }
            
            // Extract size
            size_t size_start = object_xml.find("<Size>") + 6;
            size_t size_end = object_xml.find("</Size>");
            if (size_start != std::string::npos && size_end != std::string::npos) {
                std::string size_str = object_xml.substr(size_start, size_end - size_start);
                object.size = std::stoull(size_str);
            }
            
            // Extract ETag
            size_t etag_start = object_xml.find("<ETag>") + 6;
            size_t etag_end = object_xml.find("</ETag>");
            if (etag_start != std::string::npos && etag_end != std::string::npos) {
                object.etag = object_xml.substr(etag_start, etag_end - etag_start);
                // Remove quotes from ETag
                if (!object.etag.empty() && object.etag.front() == '"') {
                    object.etag.erase(0, 1);
                }
                if (!object.etag.empty() && object.etag.back() == '"') {
                    object.etag.pop_back();
                }
            }
            
            // Extract last modified
            size_t mod_start = object_xml.find("<LastModified>") + 14;
            size_t mod_end = object_xml.find("</LastModified>");
            if (mod_start != std::string::npos && mod_end != std::string::npos) {
                object.last_modified = object_xml.substr(mod_start, mod_end - mod_start);
            }
            
            objects.push_back(object);
            pos = end_pos + 11;
        }
    }
    
    return result;
}

int VastS3Client::putObject(const std::string& bucket_name, const std::string& object_key, 
                           const std::string& file_path, const std::map<std::string, std::string>& metadata) {
    clearError();
    
    FILE* file = fopen(file_path.c_str(), "rb");
    if (!file) {
        setError(-1, "Failed to open file: " + file_path);
        return -1;
    }
    
    // Get file size
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    std::map<std::string, std::string> headers;
    headers["Content-Length"] = std::to_string(file_size);
    
    // Add metadata headers
    for (const auto& meta : metadata) {
        headers["x-amz-meta-" + meta.first] = meta.second;
    }
    
    S3Response response;
    int result = makeS3RequestWithFile("PUT", bucket_name, object_key, file, file_size, headers, response);
    
    fclose(file);
    return result;
}

int VastS3Client::getObject(const std::string& bucket_name, const std::string& object_key, 
                           const std::string& file_path, uint64_t offset, uint64_t length) {
    clearError();
    
    FILE* file = fopen(file_path.c_str(), "wb");
    if (!file) {
        setError(-1, "Failed to create file: " + file_path);
        return -1;
    }
    
    std::map<std::string, std::string> headers;
    if (offset > 0 || length > 0) {
        std::string range = "bytes=" + std::to_string(offset) + "-";
        if (length > 0) {
            range += std::to_string(offset + length - 1);
        }
        headers["Range"] = range;
    }
    
    S3Response response;
    response.output_file = file;
    
    int result = makeS3Request("GET", bucket_name, object_key, "", {}, response, headers);
    
    fclose(file);
    
    if (result != 0) {
        // Clean up failed download
        remove(file_path.c_str());
    }
    
    return result;
}

int VastS3Client::headObject(const std::string& bucket_name, const std::string& object_key, 
                            VastS3ObjectInfo& object_info) {
    clearError();
    
    S3Response response;
    int result = makeS3Request("HEAD", bucket_name, object_key, "", {}, response);
    
    if (result == 0) {
        object_info.key = object_key;
        
        auto it = response.headers.find("Content-Length");
        if (it != response.headers.end()) {
            object_info.size = std::stoull(it->second);
        }
        
        it = response.headers.find("ETag");
        if (it != response.headers.end()) {
            object_info.etag = it->second;
            // Remove quotes from ETag
            if (!object_info.etag.empty() && object_info.etag.front() == '"') {
                object_info.etag.erase(0, 1);
            }
            if (!object_info.etag.empty() && object_info.etag.back() == '"') {
                object_info.etag.pop_back();
            }
        }
        
        it = response.headers.find("Last-Modified");
        if (it != response.headers.end()) {
            object_info.last_modified = it->second;
        }
        
        // Extract metadata
        for (const auto& header : response.headers) {
            if (header.first.substr(0, 11) == "x-amz-meta-") {
                object_info.metadata[header.first.substr(11)] = header.second;
            }
        }
    }
    
    return result;
}

int VastS3Client::deleteObject(const std::string& bucket_name, const std::string& object_key) {
    clearError();
    
    S3Response response;
    return makeS3Request("DELETE", bucket_name, object_key, "", {}, response);
}

int VastS3Client::copyObject(const std::string& source_bucket, const std::string& source_key,
                            const std::string& dest_bucket, const std::string& dest_key) {
    clearError();
    
    std::map<std::string, std::string> headers;
    headers["x-amz-copy-source"] = "/" + source_bucket + "/" + urlEncode(source_key);
    
    S3Response response;
    return makeS3Request("PUT", dest_bucket, dest_key, "", {}, response, headers);
}

int VastS3Client::initiateMultipartUpload(const std::string& bucket_name, const std::string& object_key,
                                         std::string& upload_id) {
    clearError();
    
    std::map<std::string, std::string> query_params;
    query_params["uploads"] = "";
    
    S3Response response;
    int result = makeS3Request("POST", bucket_name, object_key, "", query_params, response);
    
    if (result == 0) {
        // Extract upload ID from XML response
        size_t id_start = response.body.find("<UploadId>") + 10;
        size_t id_end = response.body.find("</UploadId>");
        if (id_start != std::string::npos && id_end != std::string::npos) {
            upload_id = response.body.substr(id_start, id_end - id_start);
        } else {
            setError(-1, "Failed to parse upload ID from response");
            return -1;
        }
    }
    
    return result;
}

int VastS3Client::uploadPart(const std::string& bucket_name, const std::string& object_key,
                            const std::string& upload_id, int part_number, 
                            const std::string& file_path, uint64_t offset, uint64_t size,
                            std::string& etag) {
    clearError();
    
    FILE* file = fopen(file_path.c_str(), "rb");
    if (!file) {
        setError(-1, "Failed to open file: " + file_path);
        return -1;
    }
    
    fseek(file, offset, SEEK_SET);
    
    std::map<std::string, std::string> query_params;
    query_params["partNumber"] = std::to_string(part_number);
    query_params["uploadId"] = upload_id;
    
    std::map<std::string, std::string> headers;
    headers["Content-Length"] = std::to_string(size);
    
    S3Response response;
    int result = makeS3RequestWithFile("PUT", bucket_name, object_key, file, size, headers, response, query_params);
    
    fclose(file);
    
    if (result == 0) {
        auto it = response.headers.find("ETag");
        if (it != response.headers.end()) {
            etag = it->second;
        } else {
            setError(-1, "Failed to get ETag from upload part response");
            return -1;
        }
    }
    
    return result;
}

int VastS3Client::completeMultipartUpload(const std::string& bucket_name, const std::string& object_key,
                                         const std::string& upload_id, 
                                         const std::vector<VastS3PartInfo>& parts) {
    clearError();
    
    // Build XML body for complete multipart upload
    std::ostringstream xml;
    xml << "<CompleteMultipartUpload>";
    for (const auto& part : parts) {
        xml << "<Part>";
        xml << "<PartNumber>" << part.part_number << "</PartNumber>";
        xml << "<ETag>" << part.etag << "</ETag>";
        xml << "</Part>";
    }
    xml << "</CompleteMultipartUpload>";
    
    std::string xml_body = xml.str();
    
    std::map<std::string, std::string> query_params;
    query_params["uploadId"] = upload_id;
    
    std::map<std::string, std::string> headers;
    headers["Content-Type"] = "application/xml";
    headers["Content-Length"] = std::to_string(xml_body.length());
    
    S3Response response;
    return makeS3RequestWithBody("POST", bucket_name, object_key, xml_body, headers, response, query_params);
}

int VastS3Client::abortMultipartUpload(const std::string& bucket_name, const std::string& object_key,
                                      const std::string& upload_id) {
    clearError();
    
    std::map<std::string, std::string> query_params;
    query_params["uploadId"] = upload_id;
    
    S3Response response;
    return makeS3Request("DELETE", bucket_name, object_key, "", query_params, response);
}

// Private helper methods
std::string VastS3Client::buildS3Url(const std::string& bucket_name, const std::string& object_key,
                                     const std::map<std::string, std::string>& query_params) const {
    std::string url = m_s3_endpoint;
    
    if (!bucket_name.empty()) {
        if (url.find("://") != std::string::npos) {
            // Extract protocol
            size_t proto_end = url.find("://") + 3;
            std::string protocol = url.substr(0, proto_end);
            std::string host = url.substr(proto_end);
            
            // Use virtual-hosted-style URLs
            url = protocol + bucket_name + "." + host;
        }
    }
    
    if (!object_key.empty()) {
        if (url.back() != '/') {
            url += "/";
        }
        url += urlEncode(object_key);
    }
    
    // Add query parameters
    if (!query_params.empty()) {
        url += "?";
        bool first = true;
        for (const auto& param : query_params) {
            if (!first) url += "&";
            url += urlEncode(param.first);
            if (!param.second.empty()) {
                url += "=" + urlEncode(param.second);
            }
            first = false;
        }
    }
    
    return url;
}

int VastS3Client::makeS3Request(const std::string& method, const std::string& bucket_name,
                               const std::string& object_key, const std::string& payload,
                               const std::map<std::string, std::string>& query_params,
                               S3Response& response, const std::map<std::string, std::string>& extra_headers) {
    
    std::string url = buildS3Url(bucket_name, object_key, query_params);
    
    // Generate AWS Signature Version 4
    std::map<std::string, std::string> headers = extra_headers;
    std::string auth_header = generateAwsSignature(method, bucket_name, object_key, query_params, headers, payload);
    headers["Authorization"] = auth_header;
    
    return makeHttpRequest(method, url, payload, headers, response);
}

int VastS3Client::makeS3RequestWithFile(const std::string& method, const std::string& bucket_name,
                                       const std::string& object_key, FILE* file, uint64_t file_size,
                                       const std::map<std::string, std::string>& extra_headers,
                                       S3Response& response, const std::map<std::string, std::string>& query_params) {
    
    std::string url = buildS3Url(bucket_name, object_key, query_params);
    
    std::map<std::string, std::string> headers = extra_headers;
    
    // For file uploads, we need to calculate the signature with the file content
    // This is simplified - in production, you'd want to use streaming signatures
    std::string auth_header = generateAwsSignature(method, bucket_name, object_key, query_params, headers, "");
    headers["Authorization"] = auth_header;
    
    return makeHttpRequestWithFile(method, url, file, file_size, headers, response);
}

int VastS3Client::makeS3RequestWithBody(const std::string& method, const std::string& bucket_name,
                                       const std::string& object_key, const std::string& body,
                                       const std::map<std::string, std::string>& extra_headers,
                                       S3Response& response, const std::map<std::string, std::string>& query_params) {
    
    std::string url = buildS3Url(bucket_name, object_key, query_params);
    
    std::map<std::string, std::string> headers = extra_headers;
    std::string auth_header = generateAwsSignature(method, bucket_name, object_key, query_params, headers, body);
    headers["Authorization"] = auth_header;
    
    return makeHttpRequest(method, url, body, headers, response);
}

int VastS3Client::makeHttpRequest(const std::string& method, const std::string& url,
                                 const std::string& body, const std::map<std::string, std::string>& headers,
                                 S3Response& response) {
    if (!m_http_client) {
        setError(-1, "HTTP client not initialized");
        return -1;
    }
    
    CURL* curl = (CURL*)m_http_client;
    struct curl_slist* curl_headers = nullptr;
    
    // Reset curl handle
    curl_easy_reset(curl);
    
    // Set basic options
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, HeaderCallback);
    curl_easy_setopt(curl, CURLOPT_HEADERDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, m_timeout_seconds);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    
    // SSL verification
    if (!m_verify_ssl) {
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    }
    
    // Set headers
    for (const auto& header : headers) {
        std::string header_line = header.first + ": " + header.second;
        curl_headers = curl_slist_append(curl_headers, header_line.c_str());
    }
    
    if (curl_headers) {
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, curl_headers);
    }
    
    // Set method and body
    if (method == "GET") {
        curl_easy_setopt(curl, CURLOPT_HTTPGET, 1L);
    } else if (method == "HEAD") {
        curl_easy_setopt(curl, CURLOPT_NOBODY, 1L);
    } else if (method == "POST") {
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        if (!body.empty()) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, body.length());
        }
    } else if (method == "PUT") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
        if (!body.empty()) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, body.length());
        }
    } else if (method == "DELETE") {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
    
    // Perform the request
    CURLcode res = curl_easy_perform(curl);
    
    // Cleanup headers
    if (curl_headers) {
        curl_slist_free_all(curl_headers);
    }
    
    if (res != CURLE_OK) {
        setError(res, curl_easy_strerror(res));
        return -1;
    }
    
    // Get response code
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response.response_code);
    
    bool success = (response.response_code >= 200 && response.response_code < 300);
    if (!success) {
        setError(response.response_code, "HTTP " + std::to_string(response.response_code));
    }
    
    return success ? 0 : -1;
}

int VastS3Client::makeHttpRequestWithFile(const std::string& method, const std::string& url,
                                         FILE* file, uint64_t file_size, 
                                         const std::map<std::string, std::string>& headers,
                                         S3Response& response) {
    if (!m_http_client) {
        setError(-1, "HTTP client not initialized");
        return -1;
    }
    
    CURL* curl = (CURL*)m_http_client;
    struct curl_slist* curl_headers = nullptr;
    
    // Reset curl handle
    curl_easy_reset(curl);
    
    // Set basic options
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, HeaderCallback);
    curl_easy_setopt(curl, CURLOPT_HEADERDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, m_timeout_seconds);
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    
    // SSL verification
    if (!m_verify_ssl) {
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    }
    
    // Set headers
    for (const auto& header : headers) {
        std::string header_line = header.first + ": " + header.second;
        curl_headers = curl_slist_append(curl_headers, header_line.c_str());
    }
    
    if (curl_headers) {
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, curl_headers);
    }
    
    // Set upload options
    curl_easy_setopt(curl, CURLOPT_UPLOAD, 1L);
    curl_easy_setopt(curl, CURLOPT_READFUNCTION, ReadCallback);
    curl_easy_setopt(curl, CURLOPT_READDATA, file);
    curl_easy_setopt(curl, CURLOPT_INFILESIZE_LARGE, (curl_off_t)file_size);
    
    // Perform the request
    CURLcode res = curl_easy_perform(curl);
    
    // Cleanup headers
    if (curl_headers) {
        curl_slist_free_all(curl_headers);
    }
    
    if (res != CURLE_OK) {
        setError(res, curl_easy_strerror(res));
        return -1;
    }
    
    // Get response code
    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response.response_code);
    
    bool success = (response.response_code >= 200 && response.response_code < 300);
    if (!success) {
        setError(response.response_code, "HTTP " + std::to_string(response.response_code));
    }
    
    return success ? 0 : -1;
}

std::string VastS3Client::generateAwsSignature(const std::string& method, const std::string& bucket_name,
                                              const std::string& object_key, 
                                              const std::map<std::string, std::string>& query_params,
                                              std::map<std::string, std::string>& headers,
                                              const std::string& payload) {
    // AWS Signature Version 4 implementation
    // This is a simplified version - in production, use AWS SDK or a complete implementation
    
    // Get current time
    time_t now = time(nullptr);
    struct tm* utc_tm = gmtime(&now);
    
    char timestamp[17];
    strftime(timestamp, sizeof(timestamp), "%Y%m%dT%H%M%SZ", utc_tm);
    
    char datestamp[9];
    strftime(datestamp, sizeof(datestamp), "%Y%m%d", utc_tm);
    
    // Add required headers
    headers["Host"] = extractHostFromEndpoint();
    headers["X-Amz-Date"] = timestamp;
    
    // Calculate payload hash
    std::string payload_hash = sha256Hash(payload);
    headers["X-Amz-Content-Sha256"] = payload_hash;
    
    // Create canonical request
    std::string canonical_uri = "/";
    if (!object_key.empty()) {
        canonical_uri += urlEncode(object_key, true);
    }
    
    std::string canonical_query = "";
    if (!query_params.empty()) {
        std::vector<std::string> sorted_params;
        for (const auto& param : query_params) {
            std::string param_str = urlEncode(param.first, true);
            if (!param.second.empty()) {
                param_str += "=" + urlEncode(param.second, true);
            } else {
                param_str += "=";
            }
            sorted_params.push_back(param_str);
        }
        std::sort(sorted_params.begin(), sorted_params.end());
        
        for (size_t i = 0; i < sorted_params.size(); ++i) {
            if (i > 0) canonical_query += "&";
            canonical_query += sorted_params[i];
        }
    }
    
    std::string canonical_headers = "";
    std::string signed_headers = "";
    std::vector<std::string> header_names;
    
    for (const auto& header : headers) {
        std::string lower_name = header.first;
        std::transform(lower_name.begin(), lower_name.end(), lower_name.begin(), ::tolower);
        header_names.push_back(lower_name);
    }
    std::sort(header_names.begin(), header_names.end());
    
    for (size_t i = 0; i < header_names.size(); ++i) {
        if (i > 0) signed_headers += ";";
        signed_headers += header_names[i];
        
        // Find the header value (case-insensitive)
        std::string header_value;
        for (const auto& header : headers) {
            std::string lower_name = header.first;
            std::transform(lower_name.begin(), lower_name.end(), lower_name.begin(), ::tolower);
            if (lower_name == header_names[i]) {
                header_value = header.second;
                break;
            }
        }
        
        canonical_headers += header_names[i] + ":" + header_value + "\n";
    }
    
    std::string canonical_request = method + "\n" + canonical_uri + "\n" + canonical_query + "\n" + 
                                   canonical_headers + "\n" + signed_headers + "\n" + payload_hash;
    
    // Create string to sign
    std::string algorithm = "AWS4-HMAC-SHA256";
    std::string credential_scope = std::string(datestamp) + "/" + m_region + "/s3/aws4_request";
    std::string string_to_sign = algorithm + "\n" + timestamp + "\n" + credential_scope + "\n" + 
                                sha256Hash(canonical_request);
    
    // Calculate signature
    std::string signature = calculateSignature(string_to_sign, datestamp);
    
    // Create authorization header
    std::string credential = m_access_key + "/" + credential_scope;
    return algorithm + " Credential=" + credential + ", SignedHeaders=" + signed_headers + ", Signature=" + signature;
}

std::string VastS3Client::extractHostFromEndpoint() const {
    std::string host = m_s3_endpoint;
    
    // Remove protocol
    size_t proto_pos = host.find("://");
    if (proto_pos != std::string::npos) {
        host = host.substr(proto_pos + 3);
    }
    
    // Remove path
    size_t path_pos = host.find("/");
    if (path_pos != std::string::npos) {
        host = host.substr(0, path_pos);
    }
    
    return host;
}

std::string VastS3Client::urlEncode(const std::string& value, bool encode_slash) const {
    std::ostringstream encoded;
    encoded.fill('0');
    encoded << std::hex;
    
    for (unsigned char c : value) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~' || 
            (!encode_slash && c == '/')) {
            encoded << c;
        } else {
            encoded << std::uppercase;
            encoded << '%' << std::setw(2) << int(c);
            encoded << std::nouppercase;
        }
    }
    
    return encoded.str();
}

std::string VastS3Client::sha256Hash(const std::string& data) const {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, data.c_str(), data.length());
    SHA256_Final(hash, &sha256);
    
    std::ostringstream ss;
    ss << std::hex << std::setfill('0');
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::setw(2) << static_cast<unsigned>(hash[i]);
    }
    return ss.str();
}

std::string VastS3Client::calculateSignature(const std::string& string_to_sign, const std::string& datestamp) const {
    // AWS4 signature calculation
    std::string k_date = hmacSha256("AWS4" + m_secret_key, datestamp);
    std::string k_region = hmacSha256(k_date, m_region);
    std::string k_service = hmacSha256(k_region, "s3");
    std::string k_signing = hmacSha256(k_service, "aws4_request");
    
    std::string signature_bytes = hmacSha256(k_signing, string_to_sign);
    
    // Convert to hex string
    std::ostringstream ss;
    ss << std::hex << std::setfill('0');
    for (unsigned char c : signature_bytes) {
        ss << std::setw(2) << static_cast<unsigned>(c);
    }
    return ss.str();
}

std::string VastS3Client::hmacSha256(const std::string& key, const std::string& data) const {
    unsigned char* digest = HMAC(EVP_sha256(), key.c_str(), key.length(),
                                 (unsigned char*)data.c_str(), data.length(), nullptr, nullptr);
    
    return std::string((char*)digest, SHA256_DIGEST_LENGTH);
}

void VastS3Client::setError(int error_code, const std::string& error_msg) {
    m_last_error = error_code;
    m_last_error_msg = error_msg;
}

void VastS3Client::clearError() {
    m_last_error = 0;
    m_last_error_msg.clear();
}