/*
 *************************************************************************
 * Vast Data REST Client
 * Handles HTTP communication with Vast Data VMS REST API
 *************************************************************************
 */

#ifndef _VAST_REST_CLIENT_H_
#define _VAST_REST_CLIENT_H_

#include <string>
#include <map>
#include <vector>
#include <memory>

// JSON response structure
struct VastApiResponse {
    int status_code;
    std::string body;
    std::map<std::string, std::string> headers;
    bool success;
    std::string error_message;
};

// Authentication tokens from VMS API
struct VastAuthTokens {
    std::string access_token;
    std::string refresh_token;
    std::string user_type;
    long expires_at;
};

// S3 key pair from Vast Data API
struct VastS3KeyPair {
    std::string access_key;
    std::string secret_key;
    bool enabled;
};

// View/Volume information from VMS API
struct VastViewInfo {
    std::string name;
    std::string path;
    std::string tenant_name;
    std::string policy_name;
    bool nfs_enabled;
    bool smb_enabled;
    bool s3_enabled;
    uint64_t capacity_bytes;
    uint64_t used_bytes;
};

// S3 bucket information
struct VastS3BucketInfo {
    std::string name;
    std::string tenant_name;
    bool versioning_enabled;
    std::string lifecycle_policy;
    uint64_t object_count;
    uint64_t size_bytes;
};

// S3 Key structure for managing S3 credentials
struct VastS3Key {
    std::string name;
    std::string access_key;
    std::string secret_key;
    std::string user_name;
    std::string tenant_name;
    std::string created_at;
    bool enabled;
    
    VastS3Key() : enabled(true) {}
};

class VastRestClient {
public:
    VastRestClient();
    ~VastRestClient();

    // Connection management
    int initialize(const std::string& vms_endpoint, bool verify_ssl = true);
    void setDefaultHeaders(const std::map<std::string, std::string>& headers);
    void setTimeout(int timeout_seconds);

    // Authentication API calls
    int authenticate(const std::string& username, const std::string& password, VastAuthTokens& tokens);
    int refreshToken(const std::string& refresh_token, VastAuthTokens& tokens);
    int logout();

    // VMS System Information
    int getVmsInfo(std::map<std::string, std::string>& vms_info);
    int getClusterStatus(std::map<std::string, std::string>& cluster_status);

    // Views Management (NFS/SMB volumes)
    int listViews(std::vector<VastViewInfo>& views);
    int getViewInfo(const std::string& view_name, VastViewInfo& view_info);
    int createView(const VastViewInfo& view_info);
    int deleteView(const std::string& view_name);

    // S3 Bucket Management
    int listS3Buckets(std::vector<VastS3BucketInfo>& buckets);
    int getS3BucketInfo(const std::string& bucket_name, VastS3BucketInfo& bucket_info);
    int createS3Bucket(const VastS3BucketInfo& bucket_info);
    int deleteS3Bucket(const std::string& bucket_name);

    // S3 Key Management
    int listS3Keys(std::vector<VastS3Key>& s3_keys);
    int getS3Key(const std::string& key_name, VastS3Key& s3_key);
    int createS3Key(const std::string& key_name, const std::string& user_name, VastS3Key& s3_key);
    int deleteS3Key(const std::string& key_name);
    int enableS3Key(const std::string& key_name);
    int disableS3Key(const std::string& key_name);

    // Quotas Management
    int getQuotaInfo(const std::string& path, std::map<std::string, uint64_t>& quota_info);
    int setQuota(const std::string& path, uint64_t hard_limit, uint64_t soft_limit);

    // Snapshots Management
    int listSnapshots(const std::string& path, std::vector<std::string>& snapshots);
    int createSnapshot(const std::string& path, const std::string& snapshot_name);
    int deleteSnapshot(const std::string& path, const std::string& snapshot_name);

    // Replication Management
    int listReplicationPeers(std::vector<std::string>& peers);
    int getReplicationStatus(const std::string& path, std::map<std::string, std::string>& status);

    // Generic API methods
    int get(const std::string& endpoint, VastApiResponse& response);
    int post(const std::string& endpoint, const std::string& json_body, VastApiResponse& response);
    int put(const std::string& endpoint, const std::string& json_body, VastApiResponse& response);
    int patch(const std::string& endpoint, const std::string& json_body, VastApiResponse& response);
    int delete_request(const std::string& endpoint, VastApiResponse& response);

    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

private:
    std::string m_vms_endpoint;
    std::string m_auth_token;
    std::map<std::string, std::string> m_default_headers;
    int m_timeout_seconds;
    bool m_verify_ssl;
    
    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;

    // HTTP client implementation (could use libcurl, etc.)
    void* m_http_client;

    // Helper methods
    std::string buildUrl(const std::string& endpoint) const;
    int makeHttpRequest(const std::string& method, const std::string& url, 
                       const std::string& body, VastApiResponse& response);
    void setError(int error_code, const std::string& error_msg);
    void clearError();
    std::string escapeJsonString(const std::string& input);
    bool parseJsonResponse(const std::string& json, std::map<std::string, std::string>& result);
};

#endif /* _VAST_REST_CLIENT_H_ */