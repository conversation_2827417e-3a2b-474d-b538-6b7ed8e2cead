#
# Makefile for Vast Data OpenStorage Plugin
#

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++11 -fPIC -Wall -Wextra -O2 -DVAST_PGN_EXPORTS
INCLUDES = -I../OST-SDK-11.1.1/src/include -I.
LIBS = -lcurl -lssl -lcrypto -ljson

# Target settings
TARGET = libvastplugin.so
SOURCES = vastplugin.cpp VastStorageServer.cpp VastRestClient.cpp VastS3Client.cpp
OBJECTS = $(SOURCES:.cpp=.o)

# Build rules
all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CXX) -shared -o $@ $^ $(LIBS)

%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Dependencies
vastplugin.o: vastplugin.cpp vastplugin.h VastStorageServer.h VastRestClient.h VastS3Client.h
VastStorageServer.o: VastStorageServer.cpp VastStorageServer.h VastRestClient.h VastS3Client.h
VastRestClient.o: VastRestClient.cpp VastRestClient.h
VastS3Client.o: VastS3Client.cpp VastS3Client.h

# Utility targets
clean:
	rm -f $(OBJECTS) $(TARGET)

install: $(TARGET)
	cp $(TARGET) /usr/openv/lib/

debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

.PHONY: all clean install debug

# Notes:
# - Requires libcurl for HTTP operations
# - Requires OpenSSL for HTTPS/encryption
# - Requires a JSON library (e.g., nlohmann/json or jsoncpp)
# - OST SDK headers are in ../OST-SDK-11.1.1/src/include